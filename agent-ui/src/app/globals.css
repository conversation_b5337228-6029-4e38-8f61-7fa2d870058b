@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-border-default: 255, 255, 255, 0.2;
    --scrollbar-width: 0.1rem;

    /* shadcn/ui CSS 变量 - 深色主题 */
    --background: 17 17 19; /* #111113 */
    --foreground: 250 250 250; /* #FAFAFA */
    --primary: 250 250 250; /* #FAFAFA */
    --primary-foreground: 24 24 27; /* #18181B */
    --secondary: 39 39 42; /* #27272A */
    --secondary-foreground: 250 250 250; /* #FAFAFA */
    --accent: 39 39 42; /* #27272A */
    --accent-foreground: 250 250 250; /* #FAFAFA */
    --muted: 39 39 42; /* #27272A */
    --muted-foreground: 161 161 170; /* #A1A1AA */
    --border: 39 39 42; /* #27272A */
    --input: 39 39 42; /* #27272A */
    --ring: 79 37 184; /* #4f25b8 */
    --destructive: 229 57 53; /* #E53935 */
    --destructive-foreground: 250 250 250; /* #FAFAFA */
  }

  body {
    @apply bg-background/80 text-secondary;
  }
}

/* NarrAgent 文字渐变效果 */
.narr-agent-gradient {
  background: linear-gradient(135deg, #7ac8f9 0%, #c9c9f9 50%, #8a61f6 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(161, 161, 170, 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(161, 161, 170, 0.5);
}

::-webkit-scrollbar-thumb:active {
  background: rgba(161, 161, 170, 0.7);
}

/* 针对侧边栏的特殊滚动条样式 */
.sidebar-scroll::-webkit-scrollbar {
  width: 4px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
  margin: 8px 0;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(79, 37, 184, 0.2);
  border-radius: 2px;
  transition: all 0.2s ease;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 37, 184, 0.4);
  width: 6px;
}

/* Firefox 滚动条样式 */
.sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(79, 37, 184, 0.2) transparent;
}

/* Toast 自定义样式 */
[data-sonner-toaster] {
  --normal-bg: #27272A !important;
  --normal-border: rgba(255, 255, 255, 0.2) !important;
  --normal-text: #FAFAFA !important;
  --success-bg: #16A34A !important;
  --success-border: #22C55E !important;
  --success-text: #FFFFFF !important;
  --error-bg: #DC2626 !important;
  --error-border: #EF4444 !important;
  --error-text: #FFFFFF !important;
  --warning-bg: #D97706 !important;
  --warning-border: #F59E0B !important;
  --warning-text: #FFFFFF !important;
  --info-bg: #2563EB !important;
  --info-border: #3B82F6 !important;
  --info-text: #FFFFFF !important;
}

[data-sonner-toast] {
  background: var(--normal-bg) !important;
  border: 1px solid var(--normal-border) !important;
  color: var(--normal-text) !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

[data-sonner-toast][data-type="success"] {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
  color: var(--success-text) !important;
}

[data-sonner-toast][data-type="error"] {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
  color: var(--error-text) !important;
}

[data-sonner-toast][data-type="warning"] {
  background: var(--warning-bg) !important;
  border-color: var(--warning-border) !important;
  color: var(--warning-text) !important;
}

[data-sonner-toast][data-type="info"] {
  background: var(--info-bg) !important;
  border-color: var(--info-border) !important;
  color: var(--info-text) !important;
}

[data-sonner-toast] [data-title] {
  color: inherit !important;
  font-weight: 500 !important;
}

[data-sonner-toast] [data-description] {
  color: inherit !important;
  opacity: 0.9 !important;
}

[data-sonner-toast] [data-close-button] {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: inherit !important;
}

[data-sonner-toast] [data-close-button]:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}
