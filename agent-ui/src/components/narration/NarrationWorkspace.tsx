'use client'

import { useState, useCallback, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import Icon from '@/components/ui/icon'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'sonner'
import VideoUploader from './VideoUploader'
import VideoSorter from './VideoSorter'
import { useFFmpeg } from '@/hooks/useFFmpeg'
import { ffmpegService } from '@/lib/ffmpeg'
import { 
  type NarrationMode, 
  type VideoFileInfo, 
  type ProcessingState,
  type AudioExtractionResult,
  getNarrationModeConfig,
  formatFileSize
} from '@/types/narration'

interface NarrationWorkspaceProps {
  mode: NarrationMode
  className?: string
}

export default function NarrationWorkspace({ mode, className }: NarrationWorkspaceProps) {
  const [files, setFiles] = useState<VideoFileInfo[]>([])
  const [processingState, setProcessingState] = useState<ProcessingState>({
    isLoading: false,
    isProcessing: false,
    progress: 0,
    currentStep: ''
  })
  const [audioResult, setAudioResult] = useState<AudioExtractionResult | null>(null)
  const [isMerging, setIsMerging] = useState(false)

  const ffmpeg = useFFmpeg()
  const modeConfig = getNarrationModeConfig(mode)

  // 自动加载 FFmpeg
  useEffect(() => {
    if (files.length > 0 && !ffmpeg.loadState.isLoaded && !ffmpeg.loadState.isLoading) {
      loadFFmpeg()
    }
  }, [files.length, ffmpeg.loadState.isLoaded, ffmpeg.loadState.isLoading])

  // 加载 FFmpeg
  const loadFFmpeg = useCallback(async () => {
    // 如果已经加载完成，直接返回
    if (ffmpeg.loadState.isLoaded) {
      return
    }

    // 如果正在加载，等待加载完成
    if (ffmpeg.loadState.isLoading) {
      return
    }

    try {
      setProcessingState({
        isLoading: true,
        isProcessing: false,
        progress: 0,
        currentStep: '正在加载 FFmpeg 处理引擎...'
      })

      await ffmpeg.load()

      setProcessingState({
        isLoading: false,
        isProcessing: false,
        progress: 0,
        currentStep: ''
      })

      toast.success('FFmpeg 加载完成，可以开始处理视频')
    } catch (error) {
      console.error('FFmpeg 加载失败:', error)
      setProcessingState({
        isLoading: false,
        isProcessing: false,
        progress: 0,
        currentStep: '',
        error: error instanceof Error ? error.message : '未知错误'
      })
      toast.error('FFmpeg 加载失败，请刷新页面重试')
      throw error // 重新抛出错误，让调用者知道加载失败
    }
  }, [ffmpeg])

  // 提取音频
  const extractAudio = useCallback(async (videoFile: VideoFileInfo) => {
    if (!ffmpeg.loadState.isLoaded) {
      toast.error('FFmpeg 未加载，请等待加载完成')
      return
    }

    try {
      setProcessingState({
        isLoading: false,
        isProcessing: true,
        progress: 0,
        currentStep: '正在提取音频...'
      })

      // 监听进度
      const progressHandler = (progress: number, time: number) => {
        setProcessingState(prev => ({
          ...prev,
          progress: progress * 100,
          currentStep: `正在提取音频... ${(progress * 100).toFixed(1)}%`
        }))
      }

      // 进度监听通过 hook 已经处理

      const result = await ffmpeg.extractAudio(videoFile.file, 'mp3')
      
      if (result.success && result.data) {
        const audioResult: AudioExtractionResult = {
          audioData: result.data,
          filename: `${videoFile.name.split('.')[0]}_audio.mp3`,
          duration: 0, // TODO: 获取实际时长
          size: result.data.length
        }
        
        setAudioResult(audioResult)
        
        setProcessingState({
          isLoading: false,
          isProcessing: false,
          progress: 100,
          currentStep: '音频提取完成'
        })

        toast.success('音频提取完成！')
      } else {
        throw new Error(result.error || '音频提取失败')
      }
    } catch (error) {
      console.error('音频提取失败:', error)
      setProcessingState({
        isLoading: false,
        isProcessing: false,
        progress: 0,
        currentStep: '',
        error: error instanceof Error ? error.message : '未知错误'
      })
      toast.error(`音频提取失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }, [ffmpeg])

  // 处理文件上传
  const handleFilesUploaded = useCallback((uploadedFiles: VideoFileInfo[]) => {
    setFiles(uploadedFiles)
    setAudioResult(null)

    // 单文件模式自动开始处理
    if (!modeConfig.supportMultipleFiles && uploadedFiles.length === 1) {
      // 延迟执行，确保状态更新完成
      setTimeout(async () => {
        try {
          // 确保 FFmpeg 已加载
          if (!ffmpeg.loadState.isLoaded) {
            toast.loading('正在加载 FFmpeg...')

            try {
              await loadFFmpeg()

              // 等待一小段时间确保状态同步
              await new Promise(resolve => setTimeout(resolve, 500))

              toast.dismiss()
              toast.success('FFmpeg 加载完成')
            } catch (loadError) {
              toast.dismiss()
              throw new Error(`FFmpeg 加载失败: ${loadError instanceof Error ? loadError.message : '未知错误'}`)
            }
          }

          // 开始提取音频
          toast.loading('开始处理视频...')
          await extractAudio(uploadedFiles[0])
          toast.dismiss()
          toast.success('音频提取完成')
        } catch (error) {
          console.error('自动处理失败:', error)
          toast.dismiss()

          const errorMessage = error instanceof Error ? error.message : '未知错误'

          // 根据错误类型提供更具体的提示
          if (errorMessage.includes('FFmpeg')) {
            toast.error(`FFmpeg 相关错误: ${errorMessage}`)
          } else if (errorMessage.includes('not supported')) {
            toast.error('不支持的文件格式，请选择 MP4、WebM、AVI、MOV 或 MKV 格式')
          } else {
            toast.error(`自动处理失败: ${errorMessage}，请手动重试`)
          }
        }
      }, 1500) // 增加延迟时间，确保状态稳定
    }
  }, [modeConfig, ffmpeg.loadState, extractAudio, loadFFmpeg])

  // 合并视频（短剧模式）
  const handleMergeVideos = useCallback(async () => {
    if (files.length < 2) {
      toast.error('至少需要2个视频文件才能合并')
      return
    }

    if (!ffmpeg.loadState.isLoaded) {
      toast.error('FFmpeg 未加载，请等待加载完成')
      return
    }

    setIsMerging(true)
    
    try {
      setProcessingState({
        isLoading: false,
        isProcessing: true,
        progress: 0,
        currentStep: '正在合并视频...'
      })

      // TODO: 实现视频合并逻辑
      // 这里需要使用 FFmpeg 的 concat 功能
      
      // 模拟合并过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200))
        setProcessingState(prev => ({
          ...prev,
          progress: i,
          currentStep: `正在合并视频... ${i}%`
        }))
      }

      // 合并完成后提取音频
      // 这里应该使用合并后的视频文件
      const firstFile = files[0]
      await extractAudio(firstFile)

    } catch (error) {
      console.error('视频合并失败:', error)
      toast.error(`视频合并失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsMerging(false)
    }
  }, [files, ffmpeg, extractAudio])

  // 下载音频
  const downloadAudio = useCallback(() => {
    if (!audioResult) return

    const blob = new Blob([audioResult.audioData], { type: 'audio/mp3' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = audioResult.filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success('音频文件下载开始')
  }, [audioResult])

  // 重新开始
  const handleRestart = useCallback(() => {
    setFiles([])
    setAudioResult(null)
    setProcessingState({
      isLoading: false,
      isProcessing: false,
      progress: 0,
      currentStep: ''
    })
    setIsMerging(false)
  }, [])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 主要内容区域 */}
      <div className="space-y-6">
        {/* 视频上传 */}
        <VideoUploader
          mode={mode}
          onFilesUploaded={handleFilesUploaded}
          onProcessingStateChange={setProcessingState}
        />

        {/* 短剧模式的视频排序 */}
        {modeConfig.supportMultipleFiles && files.length > 0 && (
          <VideoSorter
            files={files}
            onFilesReorder={setFiles}
            onMergeRequest={handleMergeVideos}
            isMerging={isMerging}
          />
        )}

        {/* 处理状态 */}
        <AnimatePresence>
          {(processingState.isLoading || processingState.isProcessing) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Icon type="loader" size="sm" className="animate-spin text-blue-500" />
                      <span className="font-medium">{processingState.currentStep}</span>
                    </div>
                    
                    {processingState.progress > 0 && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>处理进度</span>
                          <span>{processingState.progress.toFixed(1)}%</span>
                        </div>
                        <Progress value={processingState.progress} />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 音频提取结果 */}
        <AnimatePresence>
          {audioResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                        <Icon type="check" size="sm" className="text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <h3 className="font-medium">音频提取完成</h3>
                        <p className="text-sm text-gray-500">
                          文件大小: {formatFileSize(audioResult.size)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-3">
                      <Button
                        onClick={downloadAudio}
                        className="flex items-center gap-2"
                        style={{
                          backgroundColor: '#4f25b8',
                          color: '#ffffff',
                          border: 'none'
                        }}
                      >
                        <Icon type="download" size="xs" />
                        下载音频文件
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={handleRestart}
                        style={{
                          borderColor: '#4f25b8',
                          color: '#ffffff',
                          backgroundColor: 'transparent'
                        }}
                      >
                        <Icon type="refresh" size="xs" className="mr-2" />
                        重新开始
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 错误状态 */}
        <AnimatePresence>
          {processingState.error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="border-red-200 dark:border-red-800">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
                      <Icon type="x" size="sm" className="text-red-600 dark:text-red-400" />
                    </div>
                    <div>
                      <h3 className="font-medium text-red-600 dark:text-red-400">处理失败</h3>
                      <p className="text-sm text-red-500 dark:text-red-400">
                        {processingState.error}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <Button variant="outline" onClick={handleRestart}>
                      <Icon type="refresh" size="xs" className="mr-2" />
                      重新开始
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
